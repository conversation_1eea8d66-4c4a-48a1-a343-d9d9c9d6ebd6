"use client";

import Link from "next/link";
import { motion } from "motion/react";
import { ChevronDown } from "lucide-react";
import logo from "@/assets/09d5333d8e054a96f2830dcb78b5e03f.png";
import { Button } from "@/components/ui/button";
import lines from "@/assets/Scribble1.png";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ui/ThemeToggle";
import Image from "next/image";

const Header = () => {
  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="flex items-center justify-between pt-5 px-3 z-20 relative bg-background md:px-8"
    >
      <div className="flex items-center gap-2 md:gap-8">
        <Link href={"/"}>
          <Image
            src={logo}
            alt="logo"
            width={120}
            height={100}
            className="size-10 md:size-12 object-cover"
          />
        </Link>
        <div className="flex flex-col">
          <div className="flex gap-2 md:gap-4 text-sm">
            <Dialog>
              <DialogTrigger className="cursor-pointer text-foreground hover:text-primary transition-colors text-xs md:text-sm font-semibold">
                How it Works
                <Image
                  src={lines}
                  alt="_"
                  width={120}
                  height={100}
                  className="-translate-y-0.5 w-20"
                />
              </DialogTrigger>
              <DialogContent className="dark:bg-[#1f252c] border-2 border-secondary md:py-10">
                <DialogHeader>
                  <DialogTitle className="text-xl">How it works?</DialogTitle>
                  <DialogDescription className="text-foreground text-base ">
                    This action cannot be undone. This will permanently delete
                    your account and remove your data from our servers.
                    <div className="p-6 bg-primary rounded-lg text-black mt-6">
                      <ul className="list-disc list-inside">
                        <li>Step 1: Create an account</li>
                        <li>Step 2: Create a campaign</li>
                        <li>Step 3: Promote your campaign</li>
                        <li>Step 4: Get paid</li>
                      </ul>
                    </div>
                    <div className="mt-8 w-full flex items-center justify-center">
                      <Link
                        href={"#"}
                        className="hover:text-primary text-center text-xl"
                      >
                        I agree
                        <Image
                          src={lines}
                          alt="_"
                          width={120}
                          height={100}
                          className="-translate-y-0.5 w-20"
                        />
                      </Link>
                    </div>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>

            <Link
              href="#"
              className="text-foreground hover:text-primary transition-colors text-xs md:text-sm font-semibold"
            >
              Support
              <Image
                src={lines}
                alt="_"
                width={120}
                height={100}
                className="-translate-y-0.5 w-12"
              />
            </Link>
          </div>
          <div className="flex justify-center gap-3 scale-[0.6] md:scale-[0.70]">
            <Button
              size="icon"
              className="bg-cyan-500 hover:bg-cyan-600 rounded-full p-2"
            >
              <Image
                src="/telegram.svg"
                alt="telegram"
                width={24}
                height={24}
              />
            </Button>
            <Button
              size="icon"
              className="bg-zinc-800 hover:bg-zinc-900 rounded-full p-2"
            >
              <Image src="/X.svg" alt="x" width={24} height={24} />
            </Button>
            <Button
              size="icon"
              className="bg-indigo-500 hover:bg-indigo-600 rounded-full p-2"
            >
              <Image src="/discord.svg" alt="discord" width={24} height={24} />
            </Button>
          </div>
        </div>
      </div>
      <div className="gap-2 hidden md:flex">
        <Button
          variant={"secondary"}
          className="bg-[#6ed1ff] py-6 text-black hover:bg-blue-300/80 font-semibold"
          size={"lg"}
        >
          James earned $200 from Michael
        </Button>

        <Button
          variant={"secondary"}
          className="bg-[#ff84e4] text-black hover:bg-pink-400/80 py-6 font-semibold"
          size={"lg"}
        >
          Charlie earned $12 from USA
        </Button>

        <Button
          variant={"secondary"}
          className="bg-[#ff822f] text-black hover:bg-amber-400/80 py-6 font-semibold"
          size={"lg"}
        >
          Ayrad earned $78 from Trump
        </Button>
      </div>
      <div className="flex items-center gap-3">
        <ThemeToggle className="hidden md:block" />
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size={"lg"}
                className="rounded-full md:text-lg md:has-[>svg]:px-8 md:py-6 truncate"
              >
                0x00..cgx
                <ChevronDown className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-popover border-border p-6">
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent text-lg border-b">
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent text-lg border-b">
                Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground focus:bg-accent text-lg">
                Disconnect
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
