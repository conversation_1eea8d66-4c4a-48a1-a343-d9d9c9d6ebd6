{"name": "promotefun", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "motion": "^12.23.6", "next": "15.4.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}